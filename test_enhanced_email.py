#!/usr/bin/env python3
"""
Test script for the enhanced email system
Tests both daily and periodic email reports with sample data
"""

import sys
import os
sys.path.append('/app')

from Scheduler_Celery.utils.email_utils import send_daily_report_email
from datetime import datetime

def test_daily_email():
    """Test daily email format"""
    print("Testing daily email format...")
    
    daily_stats = {
        'total_cases_processed': 15,
        'fetch_type': 'daily',
        'ip_statistics': {
            'cases_no_ip_gained_some': 3,
            'cases_no_ip_gained_all': 2,
            'cases_some_ip_gained_more': 4,
            'cases_some_ip_gained_all': 1,
            'cases_ip_regressed': 0,
            'cases_already_complete': 5,
            'total_processed': 15
        },
        'enhanced_statistics': {
            'new_cases_count': 8,
            'title_changes_count': 2,
            'closed_cases_count': 1,
            'cases_with_more_steps': 6,
            'cases_missing_ip_before': 10,
            'cases_missing_ip_after': 5,
            'cases_gained_ip': 5,
            'ip_sources': {
                'exhibit': 4,
                'byregno': 2,
                'cn_website': 1,
                'bygoogle': 1,
                'byname': 0,
                'manual': 0
            },
            'ip_source_details': {
                'exhibit': {'cases': ['case1_court1', 'case2_court1'], 'total_found': 8},
                'byregno': {'cases': ['case3_court1'], 'total_found': 3},
                'cn_website': {'cases': ['case4_court1'], 'total_found': 2},
                'bygoogle': {'cases': ['case5_court1'], 'total_found': 1},
                'byname': {'cases': [], 'total_found': 0},
                'manual': {'cases': [], 'total_found': 0}
            }
        },
        'fetch_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'case_details': [],
        'no_cases_found': False,
        'success_count': 14
    }
    
    try:
        # Note: This will attempt to send an actual email
        # Comment out the actual send for testing
        print("Daily email stats prepared successfully")
        print(f"Total cases: {daily_stats['total_cases_processed']}")
        print(f"Fetch type: {daily_stats['fetch_type']}")
        return True
    except Exception as e:
        print(f"Error testing daily email: {e}")
        return False

def test_periodic_email():
    """Test periodic email format"""
    print("Testing periodic email format...")
    
    periodic_stats = {
        'total_cases_processed': 45,
        'fetch_type': 'weekly',
        'ip_statistics': {
            'cases_no_ip_gained_some': 8,
            'cases_no_ip_gained_all': 5,
            'cases_some_ip_gained_more': 12,
            'cases_some_ip_gained_all': 3,
            'cases_ip_regressed': 1,
            'cases_already_complete': 16,
            'total_processed': 45
        },
        'enhanced_statistics': {
            'new_cases_count': 12,
            'title_changes_count': 7,
            'closed_cases_count': 4,
            'cases_with_more_steps': 18,
            'cases_missing_ip_before': 25,
            'cases_missing_ip_after': 12,
            'cases_gained_ip': 13,
            'ip_sources': {
                'exhibit': 10,
                'byregno': 5,
                'cn_website': 3,
                'bygoogle': 2,
                'byname': 1,
                'manual': 1
            },
            'ip_source_details': {
                'exhibit': {'cases': ['case1_court1', 'case2_court1', 'case3_court1'], 'total_found': 25},
                'byregno': {'cases': ['case4_court1', 'case5_court1'], 'total_found': 12},
                'cn_website': {'cases': ['case6_court1'], 'total_found': 8},
                'bygoogle': {'cases': ['case7_court1'], 'total_found': 4},
                'byname': {'cases': ['case8_court1'], 'total_found': 2},
                'manual': {'cases': ['case9_court1'], 'total_found': 1}
            }
        },
        'fetch_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'case_details': [],
        'no_cases_found': False,
        'success_count': 42
    }
    
    try:
        print("Periodic email stats prepared successfully")
        print(f"Total cases: {periodic_stats['total_cases_processed']}")
        print(f"Fetch type: {periodic_stats['fetch_type']}")
        print(f"New cases: {periodic_stats['enhanced_statistics']['new_cases_count']}")
        print(f"Title changes: {periodic_stats['enhanced_statistics']['title_changes_count']}")
        print(f"Closed cases: {periodic_stats['enhanced_statistics']['closed_cases_count']}")
        print(f"Cases with more steps: {periodic_stats['enhanced_statistics']['cases_with_more_steps']}")
        print(f"Cases missing IP before: {periodic_stats['enhanced_statistics']['cases_missing_ip_before']}")
        print(f"Cases missing IP after: {periodic_stats['enhanced_statistics']['cases_missing_ip_after']}")
        return True
    except Exception as e:
        print(f"Error testing periodic email: {e}")
        return False

def test_no_cases_email():
    """Test no cases found email format"""
    print("Testing no cases found email format...")
    
    no_cases_stats = {
        'total_cases_processed': 0,
        'fetch_type': 'monthly_4',
        'ip_statistics': {
            'cases_no_ip_gained_some': 0,
            'cases_no_ip_gained_all': 0,
            'cases_some_ip_gained_more': 0,
            'cases_some_ip_gained_all': 0,
            'cases_ip_regressed': 0,
            'cases_already_complete': 0,
            'total_processed': 0
        },
        'enhanced_statistics': {
            'new_cases_count': 0,
            'title_changes_count': 0,
            'closed_cases_count': 0,
            'cases_with_more_steps': 0,
            'cases_missing_ip_before': 0,
            'cases_missing_ip_after': 0,
            'cases_gained_ip': 0,
            'ip_sources': {
                'exhibit': 0,
                'byregno': 0,
                'cn_website': 0,
                'bygoogle': 0,
                'byname': 0,
                'manual': 0
            },
            'ip_source_details': {
                'exhibit': {'cases': [], 'total_found': 0},
                'byregno': {'cases': [], 'total_found': 0},
                'cn_website': {'cases': [], 'total_found': 0},
                'bygoogle': {'cases': [], 'total_found': 0},
                'byname': {'cases': [], 'total_found': 0},
                'manual': {'cases': [], 'total_found': 0}
            }
        },
        'fetch_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'case_details': [],
        'no_cases_found': True,
        'success_count': 0
    }
    
    try:
        print("No cases email stats prepared successfully")
        print(f"No cases found: {no_cases_stats['no_cases_found']}")
        print(f"Fetch type: {no_cases_stats['fetch_type']}")
        return True
    except Exception as e:
        print(f"Error testing no cases email: {e}")
        return False

def main():
    """Run all tests"""
    print("=" * 50)
    print("Testing Enhanced Email System")
    print("=" * 50)
    
    tests = [
        ("Daily Email", test_daily_email),
        ("Periodic Email", test_periodic_email),
        ("No Cases Email", test_no_cases_email)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        result = test_func()
        results.append((test_name, result))
        print(f"Result: {'PASS' if result else 'FAIL'}")
    
    print("\n" + "=" * 50)
    print("Test Summary:")
    print("=" * 50)
    
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"{test_name}: {status}")
    
    all_passed = all(result for _, result in results)
    print(f"\nOverall: {'ALL TESTS PASSED' if all_passed else 'SOME TESTS FAILED'}")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
